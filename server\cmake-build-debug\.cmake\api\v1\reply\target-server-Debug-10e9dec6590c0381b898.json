{"artifacts": [{"path": "server.exe"}, {"path": "server.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 8, "parent": 0}, {"command": 1, "file": 0, "line": 17, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++17 -fdiagnostics-color=always"}], "defines": [{"backtrace": 2, "define": "SQLITE_ENABLE_COLUMN_METADATA"}, {"backtrace": 2, "define": "SQLITE_ENABLE_JSON1"}], "includes": [{"backtrace": 2, "path": "D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include"}, {"backtrace": 2, "path": "D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/sqlite3"}, {"backtrace": 2, "path": "D:/Desktop_xiao/chat-forge-master/server/thirdparty/json-develop/include"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "17"}, "sourceIndexes": [140, 141, 142, 143, 144]}], "dependencies": [{"backtrace": 2, "id": "SQLiteCpp::@7bfbb0359d215dd9211f"}, {"backtrace": 2, "id": "sqlite3::@741eb9df4aa093c9dadc"}], "id": "server::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 2, "fragment": "thirdparty\\SQLiteCpp\\libSQLiteCpp.a", "role": "libraries"}, {"backtrace": 2, "fragment": "thirdparty\\SQLiteCpp\\sqlite3\\libsqlite3.a", "role": "libraries"}, {"backtrace": 2, "fragment": "-lws2_32", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "server", "nameOnDisk": "server.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139]}, {"name": "Source Files", "sourceIndexes": [140, 141, 142, 143, 144]}], "sources": [{"backtrace": 1, "path": "CommandHandler.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "DeThread.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "chatTask.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "common.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "infos.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "out/build/x64-Debug/CMakeFiles/ShowIncludes/foo.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "session.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/adl_serializer.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/byte_container_with_subtype.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/abi_macros.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/conversions/from_json.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/conversions/to_chars.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/conversions/to_json.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/exceptions.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/hash.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/input/binary_reader.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/input/input_adapters.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/input/json_sax.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/input/lexer.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/input/parser.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/input/position_t.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/iterators/internal_iterator.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/iterators/iter_impl.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/iterators/iteration_proxy.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/iterators/iterator_traits.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/iterators/json_reverse_iterator.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/iterators/primitive_iterator.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/json_pointer.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/json_ref.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/macro_scope.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/macro_unscope.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/meta/call_std/begin.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/meta/call_std/end.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/meta/cpp_future.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/meta/detected.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/meta/identity_tag.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/meta/is_sax.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/meta/std_fs.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/meta/type_traits.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/meta/void_t.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/output/binary_writer.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/output/output_adapters.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/output/serializer.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/string_concat.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/string_escape.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/detail/value_t.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/json.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/json_fwd.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/ordered_map.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/thirdparty/hedley/hedley.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/include/nlohmann/thirdparty/hedley/hedley_undef.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/single_include/nlohmann/json.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/single_include/nlohmann/json_fwd.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/abi/config/config.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/abi/diag/diag.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/abi/include/nlohmann/json_v3_10_5.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/cmake_target_include_directories/project/Bar.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/cmake_target_include_directories/project/Foo.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/src/make_test_data_available.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/src/test_utils.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/thirdparty/Fuzzer/FuzzerCorpus.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/thirdparty/Fuzzer/FuzzerDefs.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/thirdparty/Fuzzer/FuzzerDictionary.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/thirdparty/Fuzzer/FuzzerExtFunctions.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/thirdparty/Fuzzer/FuzzerIO.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/thirdparty/Fuzzer/FuzzerInterface.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/thirdparty/Fuzzer/FuzzerInternal.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/thirdparty/Fuzzer/FuzzerMerge.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/thirdparty/Fuzzer/FuzzerMutate.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/thirdparty/Fuzzer/FuzzerOptions.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/thirdparty/Fuzzer/FuzzerRandom.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/thirdparty/Fuzzer/FuzzerSHA1.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/thirdparty/Fuzzer/FuzzerTracePC.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/thirdparty/Fuzzer/FuzzerUtil.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/thirdparty/Fuzzer/FuzzerValueBitMap.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/thirdparty/doctest/doctest.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/thirdparty/doctest/doctest_compatibility.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/json-develop/tests/thirdparty/fifo_map/fifo_map.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googlemock/include/gmock/gmock-actions.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googlemock/include/gmock/gmock-cardinalities.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googlemock/include/gmock/gmock-function-mocker.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googlemock/include/gmock/gmock-matchers.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googlemock/include/gmock/gmock-more-actions.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googlemock/include/gmock/gmock-more-matchers.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googlemock/include/gmock/gmock-nice-strict.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googlemock/include/gmock/gmock-spec-builders.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googlemock/include/gmock/gmock.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googlemock/include/gmock/internal/custom/gmock-generated-actions.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googlemock/include/gmock/internal/custom/gmock-matchers.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googlemock/include/gmock/internal/custom/gmock-port.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googlemock/include/gmock/internal/gmock-internal-utils.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googlemock/include/gmock/internal/gmock-port.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googlemock/include/gmock/internal/gmock-pp.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googlemock/test/gmock-matchers_test.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googlemock/test/gmock_link_test.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/gtest-assertion-result.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/gtest-death-test.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/gtest-matchers.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/gtest-message.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/gtest-param-test.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/gtest-printers.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/gtest-spi.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/gtest-test-part.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/gtest-typed-test.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/gtest.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/gtest_pred_impl.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/gtest_prod.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/internal/custom/gtest-port.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/internal/custom/gtest-printers.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/internal/custom/gtest.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/internal/gtest-death-test-internal.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/internal/gtest-filepath.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/internal/gtest-internal.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/internal/gtest-param-util.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/internal/gtest-port-arch.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/internal/gtest-port.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/internal/gtest-string.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/include/gtest/internal/gtest-type-util.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/samples/prime_tables.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/samples/sample1.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/samples/sample2.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/samples/sample3-inl.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/samples/sample4.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/src/gtest-internal-inl.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/test/googletest-param-test-test.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/test/gtest-typed-test_test.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/googletest/googletest/test/production.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/Assertion.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/Backup.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/Column.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/Database.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/Exception.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/ExecuteMany.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/SQLiteCpp.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/Savepoint.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/Statement.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/Transaction.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/Utils.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/VariadicBind.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/sqlite3/sqlite3.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "CommandHandler.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "chatTask.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "common.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "session.cpp", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}