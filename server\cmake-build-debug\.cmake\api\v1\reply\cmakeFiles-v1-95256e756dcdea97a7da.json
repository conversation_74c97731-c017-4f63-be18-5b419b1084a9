{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "cmake-build-debug/CMakeFiles/3.28.1/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "cmake-build-debug/CMakeFiles/3.28.1/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "cmake-build-debug/CMakeFiles/3.28.1/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU.cmake"}, {"isGenerated": true, "path": "cmake-build-debug/CMakeFiles/3.28.1/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-windres.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-C-ABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-CXX-ABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"path": "thirdparty/sQLitecpp/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/GNUInstallDirs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakePackageConfigHelpers.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/WriteBasicConfigVersionFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/BasicConfigVersion-AnyNewerVersion.cmake.in"}, {"path": "thirdparty/sQLitecpp/cmake/SQLiteCppConfig.cmake.in"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/FindPythonInterp.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"path": "thirdparty/sQLitecpp/sqlite3/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/GNUInstallDirs.cmake"}, {"path": "thirdparty/json-develop/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/ExternalProject.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/ExternalProject/shared_internal_commands.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/GNUInstallDirs.cmake"}, {"path": "thirdparty/json-develop/cmake/pkg-config.pc.in"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakePackageConfigHelpers.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/WriteBasicConfigVersionFile.cmake"}, {"path": "thirdparty/json-develop/cmake/nlohmann_jsonConfigVersion.cmake.in"}, {"path": "thirdparty/json-develop/cmake/config.cmake.in"}], "kind": "cmakeFiles", "paths": {"build": "D:/Desktop_xiao/chat-forge-master/server/cmake-build-debug", "source": "D:/Desktop_xiao/chat-forge-master/server"}, "version": {"major": 1, "minor": 0}}