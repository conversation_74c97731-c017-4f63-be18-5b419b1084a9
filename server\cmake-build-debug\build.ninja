# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.28

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: server
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/Desktop_xiao/chat-forge-master/server/cmake-build-debug/
# =============================================================================
# Object build statements for EXECUTABLE target server


#############################################
# Order-only phony target for server

build cmake_object_order_depends_target_server: phony || cmake_object_order_depends_target_SQLiteCpp cmake_object_order_depends_target_sqlite3

build CMakeFiles/server.dir/CommandHandler.cpp.obj: CXX_COMPILER__server_unscanned_Debug D$:/Desktop_xiao/chat-forge-master/server/CommandHandler.cpp || cmake_object_order_depends_target_server
  DEFINES = -DSQLITE_ENABLE_COLUMN_METADATA -DSQLITE_ENABLE_JSON1
  DEP_FILE = CMakeFiles\server.dir\CommandHandler.cpp.obj.d
  FLAGS = -g -std=gnu++17 -fdiagnostics-color=always
  INCLUDES = -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/sqlite3 -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/json-develop/include
  OBJECT_DIR = CMakeFiles\server.dir
  OBJECT_FILE_DIR = CMakeFiles\server.dir

build CMakeFiles/server.dir/chatTask.cpp.obj: CXX_COMPILER__server_unscanned_Debug D$:/Desktop_xiao/chat-forge-master/server/chatTask.cpp || cmake_object_order_depends_target_server
  DEFINES = -DSQLITE_ENABLE_COLUMN_METADATA -DSQLITE_ENABLE_JSON1
  DEP_FILE = CMakeFiles\server.dir\chatTask.cpp.obj.d
  FLAGS = -g -std=gnu++17 -fdiagnostics-color=always
  INCLUDES = -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/sqlite3 -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/json-develop/include
  OBJECT_DIR = CMakeFiles\server.dir
  OBJECT_FILE_DIR = CMakeFiles\server.dir

build CMakeFiles/server.dir/common.cpp.obj: CXX_COMPILER__server_unscanned_Debug D$:/Desktop_xiao/chat-forge-master/server/common.cpp || cmake_object_order_depends_target_server
  DEFINES = -DSQLITE_ENABLE_COLUMN_METADATA -DSQLITE_ENABLE_JSON1
  DEP_FILE = CMakeFiles\server.dir\common.cpp.obj.d
  FLAGS = -g -std=gnu++17 -fdiagnostics-color=always
  INCLUDES = -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/sqlite3 -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/json-develop/include
  OBJECT_DIR = CMakeFiles\server.dir
  OBJECT_FILE_DIR = CMakeFiles\server.dir

build CMakeFiles/server.dir/main.cpp.obj: CXX_COMPILER__server_unscanned_Debug D$:/Desktop_xiao/chat-forge-master/server/main.cpp || cmake_object_order_depends_target_server
  DEFINES = -DSQLITE_ENABLE_COLUMN_METADATA -DSQLITE_ENABLE_JSON1
  DEP_FILE = CMakeFiles\server.dir\main.cpp.obj.d
  FLAGS = -g -std=gnu++17 -fdiagnostics-color=always
  INCLUDES = -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/sqlite3 -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/json-develop/include
  OBJECT_DIR = CMakeFiles\server.dir
  OBJECT_FILE_DIR = CMakeFiles\server.dir

build CMakeFiles/server.dir/session.cpp.obj: CXX_COMPILER__server_unscanned_Debug D$:/Desktop_xiao/chat-forge-master/server/session.cpp || cmake_object_order_depends_target_server
  DEFINES = -DSQLITE_ENABLE_COLUMN_METADATA -DSQLITE_ENABLE_JSON1
  DEP_FILE = CMakeFiles\server.dir\session.cpp.obj.d
  FLAGS = -g -std=gnu++17 -fdiagnostics-color=always
  INCLUDES = -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/sqlite3 -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/json-develop/include
  OBJECT_DIR = CMakeFiles\server.dir
  OBJECT_FILE_DIR = CMakeFiles\server.dir


# =============================================================================
# Link build statements for EXECUTABLE target server


#############################################
# Link the executable server.exe

build server.exe: CXX_EXECUTABLE_LINKER__server_Debug CMakeFiles/server.dir/CommandHandler.cpp.obj CMakeFiles/server.dir/chatTask.cpp.obj CMakeFiles/server.dir/common.cpp.obj CMakeFiles/server.dir/main.cpp.obj CMakeFiles/server.dir/session.cpp.obj | thirdparty/SQLiteCpp/libSQLiteCpp.a thirdparty/SQLiteCpp/sqlite3/libsqlite3.a || thirdparty/SQLiteCpp/libSQLiteCpp.a thirdparty/SQLiteCpp/sqlite3/libsqlite3.a
  FLAGS = -g
  LINK_LIBRARIES = thirdparty/SQLiteCpp/libSQLiteCpp.a  thirdparty/SQLiteCpp/sqlite3/libsqlite3.a  -lws2_32  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32
  OBJECT_DIR = CMakeFiles\server.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = server.exe
  TARGET_IMPLIB = libserver.dll.a
  TARGET_PDB = server.exe.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" --regenerate-during-build -SD:\Desktop_xiao\chat-forge-master\server -BD:\Desktop_xiao\chat-forge-master\server\cmake-build-debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake"
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Desktop_xiao/chat-forge-master/server/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target SQLiteCpp


#############################################
# Order-only phony target for SQLiteCpp

build cmake_object_order_depends_target_SQLiteCpp: phony || cmake_object_order_depends_target_sqlite3

build thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Backup.cpp.obj: CXX_COMPILER__SQLiteCpp_unscanned_Debug D$:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/src/Backup.cpp || cmake_object_order_depends_target_SQLiteCpp
  DEFINES = -DSQLITE_ENABLE_COLUMN_METADATA -DSQLITE_ENABLE_JSON1
  DEP_FILE = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir\src\Backup.cpp.obj.d
  FLAGS = -Wall -Wextra -Wpedantic -Wswitch-enum -Wshadow -Wno-long-long -g -std=gnu++17 -fdiagnostics-color=always
  INCLUDES = -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/sqlite3
  OBJECT_DIR = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir
  OBJECT_FILE_DIR = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir\src

build thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Column.cpp.obj: CXX_COMPILER__SQLiteCpp_unscanned_Debug D$:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/src/Column.cpp || cmake_object_order_depends_target_SQLiteCpp
  DEFINES = -DSQLITE_ENABLE_COLUMN_METADATA -DSQLITE_ENABLE_JSON1
  DEP_FILE = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir\src\Column.cpp.obj.d
  FLAGS = -Wall -Wextra -Wpedantic -Wswitch-enum -Wshadow -Wno-long-long -g -std=gnu++17 -fdiagnostics-color=always
  INCLUDES = -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/sqlite3
  OBJECT_DIR = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir
  OBJECT_FILE_DIR = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir\src

build thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Database.cpp.obj: CXX_COMPILER__SQLiteCpp_unscanned_Debug D$:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/src/Database.cpp || cmake_object_order_depends_target_SQLiteCpp
  DEFINES = -DSQLITE_ENABLE_COLUMN_METADATA -DSQLITE_ENABLE_JSON1
  DEP_FILE = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir\src\Database.cpp.obj.d
  FLAGS = -Wall -Wextra -Wpedantic -Wswitch-enum -Wshadow -Wno-long-long -g -std=gnu++17 -fdiagnostics-color=always
  INCLUDES = -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/sqlite3
  OBJECT_DIR = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir
  OBJECT_FILE_DIR = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir\src

build thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Exception.cpp.obj: CXX_COMPILER__SQLiteCpp_unscanned_Debug D$:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/src/Exception.cpp || cmake_object_order_depends_target_SQLiteCpp
  DEFINES = -DSQLITE_ENABLE_COLUMN_METADATA -DSQLITE_ENABLE_JSON1
  DEP_FILE = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir\src\Exception.cpp.obj.d
  FLAGS = -Wall -Wextra -Wpedantic -Wswitch-enum -Wshadow -Wno-long-long -g -std=gnu++17 -fdiagnostics-color=always
  INCLUDES = -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/sqlite3
  OBJECT_DIR = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir
  OBJECT_FILE_DIR = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir\src

build thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Savepoint.cpp.obj: CXX_COMPILER__SQLiteCpp_unscanned_Debug D$:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/src/Savepoint.cpp || cmake_object_order_depends_target_SQLiteCpp
  DEFINES = -DSQLITE_ENABLE_COLUMN_METADATA -DSQLITE_ENABLE_JSON1
  DEP_FILE = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir\src\Savepoint.cpp.obj.d
  FLAGS = -Wall -Wextra -Wpedantic -Wswitch-enum -Wshadow -Wno-long-long -g -std=gnu++17 -fdiagnostics-color=always
  INCLUDES = -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/sqlite3
  OBJECT_DIR = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir
  OBJECT_FILE_DIR = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir\src

build thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Statement.cpp.obj: CXX_COMPILER__SQLiteCpp_unscanned_Debug D$:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/src/Statement.cpp || cmake_object_order_depends_target_SQLiteCpp
  DEFINES = -DSQLITE_ENABLE_COLUMN_METADATA -DSQLITE_ENABLE_JSON1
  DEP_FILE = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir\src\Statement.cpp.obj.d
  FLAGS = -Wall -Wextra -Wpedantic -Wswitch-enum -Wshadow -Wno-long-long -g -std=gnu++17 -fdiagnostics-color=always
  INCLUDES = -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/sqlite3
  OBJECT_DIR = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir
  OBJECT_FILE_DIR = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir\src

build thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Transaction.cpp.obj: CXX_COMPILER__SQLiteCpp_unscanned_Debug D$:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/src/Transaction.cpp || cmake_object_order_depends_target_SQLiteCpp
  DEFINES = -DSQLITE_ENABLE_COLUMN_METADATA -DSQLITE_ENABLE_JSON1
  DEP_FILE = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir\src\Transaction.cpp.obj.d
  FLAGS = -Wall -Wextra -Wpedantic -Wswitch-enum -Wshadow -Wno-long-long -g -std=gnu++17 -fdiagnostics-color=always
  INCLUDES = -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/sqlite3
  OBJECT_DIR = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir
  OBJECT_FILE_DIR = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir\src


# =============================================================================
# Link build statements for STATIC_LIBRARY target SQLiteCpp


#############################################
# Link the static library thirdparty\SQLiteCpp\libSQLiteCpp.a

build thirdparty/SQLiteCpp/libSQLiteCpp.a: CXX_STATIC_LIBRARY_LINKER__SQLiteCpp_Debug thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Backup.cpp.obj thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Column.cpp.obj thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Database.cpp.obj thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Exception.cpp.obj thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Savepoint.cpp.obj thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Statement.cpp.obj thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Transaction.cpp.obj || thirdparty/SQLiteCpp/sqlite3/libsqlite3.a
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic -Wswitch-enum -Wshadow -Wno-long-long -g
  OBJECT_DIR = thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = thirdparty\SQLiteCpp\libSQLiteCpp.a
  TARGET_PDB = SQLiteCpp.a.dbg


#############################################
# Utility command for SQLiteCpp_cpplint

build thirdparty/SQLiteCpp/SQLiteCpp_cpplint: phony thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint


#############################################
# Utility command for edit_cache

build thirdparty/SQLiteCpp/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug\thirdparty\SQLiteCpp && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build thirdparty/SQLiteCpp/edit_cache: phony thirdparty/SQLiteCpp/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build thirdparty/SQLiteCpp/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug\thirdparty\SQLiteCpp && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" --regenerate-during-build -SD:\Desktop_xiao\chat-forge-master\server -BD:\Desktop_xiao\chat-forge-master\server\cmake-build-debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build thirdparty/SQLiteCpp/rebuild_cache: phony thirdparty/SQLiteCpp/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build thirdparty/SQLiteCpp/list_install_components: phony


#############################################
# Utility command for install

build thirdparty/SQLiteCpp/CMakeFiles/install.util: CUSTOM_COMMAND thirdparty/SQLiteCpp/all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug\thirdparty\SQLiteCpp && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build thirdparty/SQLiteCpp/install: phony thirdparty/SQLiteCpp/CMakeFiles/install.util


#############################################
# Utility command for install/local

build thirdparty/SQLiteCpp/CMakeFiles/install/local.util: CUSTOM_COMMAND thirdparty/SQLiteCpp/all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug\thirdparty\SQLiteCpp && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build thirdparty/SQLiteCpp/install/local: phony thirdparty/SQLiteCpp/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build thirdparty/SQLiteCpp/CMakeFiles/install/strip.util: CUSTOM_COMMAND thirdparty/SQLiteCpp/all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug\thirdparty\SQLiteCpp && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake"
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build thirdparty/SQLiteCpp/install/strip: phony thirdparty/SQLiteCpp/CMakeFiles/install/strip.util


#############################################
# Custom command for thirdparty\SQLiteCpp\CMakeFiles\SQLiteCpp_cpplint

build thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint | ${cmake_ninja_workdir}thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug\thirdparty\SQLiteCpp && E:\YingYong\py3.8\python.exe D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/cpplint.py --output=eclipse --verbose=3 --linelength=120 D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/src/Backup.cpp D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/src/Column.cpp D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/src/Database.cpp D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/src/Exception.cpp D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/src/Savepoint.cpp D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/src/Statement.cpp D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/src/Transaction.cpp D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include/SQLiteCpp/SQLiteCpp.h D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include/SQLiteCpp/Assertion.h D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include/SQLiteCpp/Backup.h D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include/SQLiteCpp/Column.h D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include/SQLiteCpp/Database.h D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include/SQLiteCpp/Exception.h D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include/SQLiteCpp/Savepoint.h D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include/SQLiteCpp/Statement.h D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include/SQLiteCpp/Transaction.h D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include/SQLiteCpp/VariadicBind.h D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include/SQLiteCpp/ExecuteMany.h"

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target sqlite3


#############################################
# Order-only phony target for sqlite3

build cmake_object_order_depends_target_sqlite3: phony || thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir

build thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/sqlite3.c.obj: C_COMPILER__sqlite3_unscanned_Debug D$:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/sqlite3/sqlite3.c || cmake_object_order_depends_target_sqlite3
  DEFINES = -DSQLITE_ENABLE_COLUMN_METADATA -DSQLITE_ENABLE_JSON1
  DEP_FILE = thirdparty\SQLiteCpp\sqlite3\CMakeFiles\sqlite3.dir\sqlite3.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -ID:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/sqlite3
  OBJECT_DIR = thirdparty\SQLiteCpp\sqlite3\CMakeFiles\sqlite3.dir
  OBJECT_FILE_DIR = thirdparty\SQLiteCpp\sqlite3\CMakeFiles\sqlite3.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target sqlite3


#############################################
# Link the static library thirdparty\SQLiteCpp\sqlite3\libsqlite3.a

build thirdparty/SQLiteCpp/sqlite3/libsqlite3.a: C_STATIC_LIBRARY_LINKER__sqlite3_Debug thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/sqlite3.c.obj
  LANGUAGE_COMPILE_FLAGS = -g
  OBJECT_DIR = thirdparty\SQLiteCpp\sqlite3\CMakeFiles\sqlite3.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = thirdparty\SQLiteCpp\sqlite3\libsqlite3.a
  TARGET_PDB = sqlite3.a.dbg


#############################################
# Utility command for edit_cache

build thirdparty/SQLiteCpp/sqlite3/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug\thirdparty\SQLiteCpp\sqlite3 && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build thirdparty/SQLiteCpp/sqlite3/edit_cache: phony thirdparty/SQLiteCpp/sqlite3/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build thirdparty/SQLiteCpp/sqlite3/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug\thirdparty\SQLiteCpp\sqlite3 && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" --regenerate-during-build -SD:\Desktop_xiao\chat-forge-master\server -BD:\Desktop_xiao\chat-forge-master\server\cmake-build-debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build thirdparty/SQLiteCpp/sqlite3/rebuild_cache: phony thirdparty/SQLiteCpp/sqlite3/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build thirdparty/SQLiteCpp/sqlite3/list_install_components: phony


#############################################
# Utility command for install

build thirdparty/SQLiteCpp/sqlite3/CMakeFiles/install.util: CUSTOM_COMMAND thirdparty/SQLiteCpp/sqlite3/all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug\thirdparty\SQLiteCpp\sqlite3 && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build thirdparty/SQLiteCpp/sqlite3/install: phony thirdparty/SQLiteCpp/sqlite3/CMakeFiles/install.util


#############################################
# Utility command for install/local

build thirdparty/SQLiteCpp/sqlite3/CMakeFiles/install/local.util: CUSTOM_COMMAND thirdparty/SQLiteCpp/sqlite3/all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug\thirdparty\SQLiteCpp\sqlite3 && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build thirdparty/SQLiteCpp/sqlite3/install/local: phony thirdparty/SQLiteCpp/sqlite3/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build thirdparty/SQLiteCpp/sqlite3/CMakeFiles/install/strip.util: CUSTOM_COMMAND thirdparty/SQLiteCpp/sqlite3/all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug\thirdparty\SQLiteCpp\sqlite3 && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake"
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build thirdparty/SQLiteCpp/sqlite3/install/strip: phony thirdparty/SQLiteCpp/sqlite3/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Desktop_xiao/chat-forge-master/server/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build thirdparty/json-develop/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug\thirdparty\json-develop && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build thirdparty/json-develop/edit_cache: phony thirdparty/json-develop/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build thirdparty/json-develop/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug\thirdparty\json-develop && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" --regenerate-during-build -SD:\Desktop_xiao\chat-forge-master\server -BD:\Desktop_xiao\chat-forge-master\server\cmake-build-debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build thirdparty/json-develop/rebuild_cache: phony thirdparty/json-develop/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build thirdparty/json-develop/list_install_components: phony


#############################################
# Utility command for install

build thirdparty/json-develop/CMakeFiles/install.util: CUSTOM_COMMAND thirdparty/json-develop/all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug\thirdparty\json-develop && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build thirdparty/json-develop/install: phony thirdparty/json-develop/CMakeFiles/install.util


#############################################
# Utility command for install/local

build thirdparty/json-develop/CMakeFiles/install/local.util: CUSTOM_COMMAND thirdparty/json-develop/all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug\thirdparty\json-develop && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build thirdparty/json-develop/install/local: phony thirdparty/json-develop/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build thirdparty/json-develop/CMakeFiles/install/strip.util: CUSTOM_COMMAND thirdparty/json-develop/all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\Desktop_xiao\chat-forge-master\server\cmake-build-debug\thirdparty\json-develop && "D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake"
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build thirdparty/json-develop/install/strip: phony thirdparty/json-develop/CMakeFiles/install/strip.util

# =============================================================================
# Target aliases.

build SQLiteCpp: phony thirdparty/SQLiteCpp/libSQLiteCpp.a

build SQLiteCpp_cpplint: phony thirdparty/SQLiteCpp/SQLiteCpp_cpplint

build libSQLiteCpp.a: phony thirdparty/SQLiteCpp/libSQLiteCpp.a

build libsqlite3.a: phony thirdparty/SQLiteCpp/sqlite3/libsqlite3.a

build server: phony server.exe

build sqlite3: phony thirdparty/SQLiteCpp/sqlite3/libsqlite3.a

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/Desktop_xiao/chat-forge-master/server/cmake-build-debug

build all: phony server.exe thirdparty/SQLiteCpp/all thirdparty/json-develop/all

# =============================================================================

#############################################
# Folder: D:/Desktop_xiao/chat-forge-master/server/cmake-build-debug/thirdparty/SQLiteCpp

build thirdparty/SQLiteCpp/all: phony thirdparty/SQLiteCpp/libSQLiteCpp.a thirdparty/SQLiteCpp/SQLiteCpp_cpplint thirdparty/SQLiteCpp/sqlite3/all

# =============================================================================

#############################################
# Folder: D:/Desktop_xiao/chat-forge-master/server/cmake-build-debug/thirdparty/SQLiteCpp/sqlite3

build thirdparty/SQLiteCpp/sqlite3/all: phony thirdparty/SQLiteCpp/sqlite3/libsqlite3.a

# =============================================================================

#############################################
# Folder: D:/Desktop_xiao/chat-forge-master/server/cmake-build-debug/thirdparty/json-develop

build thirdparty/json-develop/all: phony

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | CMakeCache.txt CMakeFiles/3.28.1/CMakeCCompiler.cmake CMakeFiles/3.28.1/CMakeCXXCompiler.cmake CMakeFiles/3.28.1/CMakeRCCompiler.cmake CMakeFiles/3.28.1/CMakeSystem.cmake D$:/Desktop_xiao/chat-forge-master/server/CMakeLists.txt D$:/Desktop_xiao/chat-forge-master/server/thirdparty/json-develop/CMakeLists.txt D$:/Desktop_xiao/chat-forge-master/server/thirdparty/json-develop/cmake/config.cmake.in D$:/Desktop_xiao/chat-forge-master/server/thirdparty/json-develop/cmake/nlohmann_jsonConfigVersion.cmake.in D$:/Desktop_xiao/chat-forge-master/server/thirdparty/json-develop/cmake/pkg-config.pc.in D$:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/CMakeLists.txt D$:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/cmake/SQLiteCppConfig.cmake.in D$:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/sqlite3/CMakeLists.txt D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/BasicConfigVersion-AnyNewerVersion.cmake.in D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCInformation.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCXXInformation.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeGenericSystem.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakePackageConfigHelpers.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeRCInformation.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CheckCSourceCompiles.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CheckIncludeFile.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CheckLibraryExists.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU-C.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/ExternalProject.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/ExternalProject/shared_internal_commands.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/FindPackageMessage.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/FindPythonInterp.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/FindThreads.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/GNUInstallDirs.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-C-ABI.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-C.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-CXX-ABI.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-CXX.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-Initialize.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-windres.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/WindowsPaths.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/WriteBasicConfigVersionFile.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles/3.28.1/CMakeCCompiler.cmake CMakeFiles/3.28.1/CMakeCXXCompiler.cmake CMakeFiles/3.28.1/CMakeRCCompiler.cmake CMakeFiles/3.28.1/CMakeSystem.cmake D$:/Desktop_xiao/chat-forge-master/server/CMakeLists.txt D$:/Desktop_xiao/chat-forge-master/server/thirdparty/json-develop/CMakeLists.txt D$:/Desktop_xiao/chat-forge-master/server/thirdparty/json-develop/cmake/config.cmake.in D$:/Desktop_xiao/chat-forge-master/server/thirdparty/json-develop/cmake/nlohmann_jsonConfigVersion.cmake.in D$:/Desktop_xiao/chat-forge-master/server/thirdparty/json-develop/cmake/pkg-config.pc.in D$:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/CMakeLists.txt D$:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/cmake/SQLiteCppConfig.cmake.in D$:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/sqlite3/CMakeLists.txt D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/BasicConfigVersion-AnyNewerVersion.cmake.in D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCInformation.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCXXInformation.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeGenericSystem.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakePackageConfigHelpers.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeRCInformation.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CheckCSourceCompiles.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CheckIncludeFile.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/CheckLibraryExists.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU-C.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/ExternalProject.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/ExternalProject/shared_internal_commands.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/FindPackageMessage.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/FindPythonInterp.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/FindThreads.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/GNUInstallDirs.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-C-ABI.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-C.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-CXX-ABI.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-CXX.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-Initialize.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-windres.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/WindowsPaths.cmake D$:/Program$ Files/JetBrains/CLion$ 2024.1.2/bin/cmake/win/x64/share/cmake-3.28/Modules/WriteBasicConfigVersionFile.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
