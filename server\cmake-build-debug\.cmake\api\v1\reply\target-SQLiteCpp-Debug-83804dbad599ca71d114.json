{"archive": {}, "artifacts": [{"path": "thirdparty/SQLiteCpp/libSQLiteCpp.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "target_compile_definitions", "target_include_directories"], "files": ["thirdparty/sQLitecpp/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 186, "parent": 0}, {"command": 1, "file": 0, "line": 324, "parent": 0}, {"command": 2, "file": 0, "line": 251, "parent": 0}, {"command": 3, "file": 0, "line": 194, "parent": 0}, {"command": 4, "file": 0, "line": 316, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -Wpedantic -Wswitch-enum -Wshadow -Wno-long-long -g -std=gnu++17 -fdiagnostics-color=always"}], "defines": [{"backtrace": 4, "define": "SQLITE_ENABLE_COLUMN_METADATA"}, {"backtrace": 3, "define": "SQLITE_ENABLE_JSON1"}], "includes": [{"backtrace": 5, "path": "D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/include"}, {"backtrace": 3, "path": "D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/sqlite3"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6]}], "dependencies": [{"backtrace": 3, "id": "sqlite3::@741eb9df4aa093c9dadc"}], "id": "SQLiteCpp::@7bfbb0359d215dd9211f", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "C:/Program Files (x86)/server"}}, "name": "SQLiteCpp", "nameOnDisk": "libSQLiteCpp.a", "paths": {"build": "thirdparty/SQLiteCpp", "source": "thirdparty/sQLitecpp"}, "sourceGroups": [{"name": "src", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6]}, {"name": "include", "sourceIndexes": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}, {"name": "doc", "sourceIndexes": [18, 19, 20, 21]}, {"name": "scripts", "sourceIndexes": [22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/sQLitecpp/src/Backup.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/sQLitecpp/src/Column.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/sQLitecpp/src/Database.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/sQLitecpp/src/Exception.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/sQLitecpp/src/Savepoint.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/sQLitecpp/src/Statement.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/sQLitecpp/src/Transaction.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/SQLiteCpp.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/Assertion.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/Backup.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/Column.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/Database.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/Exception.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/Savepoint.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/Statement.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/Transaction.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/VariadicBind.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/include/SQLiteCpp/ExecuteMany.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/README.md", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/LICENSE.txt", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/CHANGELOG.md", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/TODO.txt", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/.editorconfig", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/.gitbugtraq", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/.github/workflows/build.yml", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/.github/workflows/subdir_example.yml", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/.gitignore", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/.gitmodules", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/.travis.yml", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/appveyor.yml", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/build.bat", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/build.sh", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/cpplint.py", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/Doxyfile", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/cmake/FindSQLite3.cmake", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/cmake/SQLiteCppConfig.cmake.in", "sourceGroupIndex": 3}], "type": "STATIC_LIBRARY"}