#include "creategroup.h"
#include "ui_creategroup.h"
#include <QTimer>
#include <QDebug>

CreateGroup::CreateGroup(SelfInfo info, TcpClient *t, QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::CreateGroup)
    , info(info)
    , t(t)
{
    ui->setupUi(this);
    Init();
    msgBox = new QMessageBox(this);
    connect(t, &TcpClient::CallClient, this, &CreateGroup::CmdHandler);
}

CreateGroup::~CreateGroup()
{
    delete ui;
}

void CreateGroup::Init()
{
    // 设置窗口属性
    this->setWindowFlags(Qt::FramelessWindowHint);
    this->setAttribute(Qt::WA_TranslucentBackground, true);
    
    // 添加阴影效果
    QGraphicsDropShadowEffect *shadow = new QGraphicsDropShadowEffect(this);
    shadow->setOffset(0, 0);
    shadow->setColor(QColor(39, 40, 43, 100));
    shadow->setBlurRadius(10);
    this->setGraphicsEffect(shadow);
    
    // 设置占位符文本
    ui->lineEdit_groupName->setPlaceholderText("请输入群名称");
    ui->textEdit_description->setPlaceholderText("请输入群描述（可选）");
    
    setWindowTitle("创建群聊");
}

void CreateGroup::on_pushButton_create_clicked()
{
    QString groupName = ui->lineEdit_groupName->text().trimmed();
    
    if (groupName.isEmpty()) {
        msgBox->setText("群名称不能为空");
        msgBox->showNormal();
        return;
    }
    
    if (groupName.length() > 20) {
        msgBox->setText("群名称不能超过20个字符");
        msgBox->showNormal();
        return;
    }
    
    QString description = ui->textEdit_description->toPlainText().trimmed();
    
    // 构建创建群聊的消息
    json msg = {
        {"cmd", cmd_group_create},
        {"creator", info.account},
        {"groupName", groupName},
        {"description", description},
        {"creatorName", info.name},
        {"creatorIcon", info.icon}
    };
    
    qDebug() << "Creating group:" << groupName;
    t->SendMsg(msg);
    
    // 禁用按钮防止重复点击
    ui->pushButton_create->setEnabled(false);
    ui->pushButton_create->setText("创建中...");
}

void CreateGroup::on_pushButton_cancel_clicked()
{
    this->close();
}

void CreateGroup::CmdHandler(json msg)
{
    int cmd = msg["cmd"].toInt();
    if (cmd == cmd_group_create) {
        ui->pushButton_create->setEnabled(true);
        ui->pushButton_create->setText("创建群聊");
        
        QString res = msg["res"].toString();
        if (res == "yes") {
            msgBox->setText("群聊创建成功！");
            msgBox->showNormal();
            
            // 创建成功后关闭对话框
            QTimer::singleShot(1500, this, &CreateGroup::close);
        } else {
            QString error = msg["error"].toString();
            if (error.isEmpty()) {
                error = "创建群聊失败，请重试";
            }
            msgBox->setText(error);
            msgBox->showNormal();
        }
    }
}
