{"archive": {}, "artifacts": [{"path": "thirdparty/SQLiteCpp/sqlite3/libsqlite3.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_compile_definitions", "target_include_directories"], "files": ["thirdparty/sQLitecpp/sqlite3/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 9, "parent": 0}, {"command": 1, "file": 0, "line": 60, "parent": 0}, {"command": 2, "file": 0, "line": 24, "parent": 0}, {"command": 2, "file": 0, "line": 30, "parent": 0}, {"command": 3, "file": 0, "line": 16, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -fdiagnostics-color=always"}], "defines": [{"backtrace": 3, "define": "SQLITE_ENABLE_COLUMN_METADATA"}, {"backtrace": 4, "define": "SQLITE_ENABLE_JSON1"}], "includes": [{"backtrace": 5, "path": "D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp/sqlite3"}], "language": "C", "sourceIndexes": [0]}], "id": "sqlite3::@741eb9df4aa093c9dadc", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "C:/Program Files (x86)/server"}}, "name": "sqlite3", "nameOnDisk": "libsqlite3.a", "paths": {"build": "thirdparty/SQLiteCpp/sqlite3", "source": "thirdparty/sQLitecpp/sqlite3"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "thirdparty/sQLitecpp/sqlite3/sqlite3.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "thirdparty/sQLitecpp/sqlite3/sqlite3.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}