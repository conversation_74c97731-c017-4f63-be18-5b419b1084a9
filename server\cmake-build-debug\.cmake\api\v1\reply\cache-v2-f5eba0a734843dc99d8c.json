{"entries": [{"name": "CMAKE_ADDR2LINE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/addr2line.exe"}, {"name": "CMAKE_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/ar.exe"}, {"name": "CMAKE_BUILD_TYPE", "properties": [{"name": "HELPSTRING", "value": "Choose the type of build, options are: None Debug Release RelWithDebInfo MinSizeRel ..."}], "type": "STRING", "value": "Debug"}, {"name": "CMAKE_CACHEFILE_DIR", "properties": [{"name": "HELPSTRING", "value": "This is the directory where this CMakeCache.txt was created"}], "type": "INTERNAL", "value": "d:/Desktop_xiao/chat-forge-master/server/cmake-build-debug"}, {"name": "CMAKE_CACHE_MAJOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Major version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "3"}, {"name": "CMAKE_CACHE_MINOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Minor version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "28"}, {"name": "CMAKE_CACHE_PATCH_VERSION", "properties": [{"name": "HELPSTRING", "value": "Patch version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_COLOR_DIAGNOSTICS", "properties": [{"name": "HELPSTRING", "value": "Enable colored diagnostics throughout."}], "type": "BOOL", "value": "ON"}, {"name": "CMAKE_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to CMake executable."}], "type": "INTERNAL", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/bin/cmake.exe"}, {"name": "CMAKE_CPACK_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cpack program executable."}], "type": "INTERNAL", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/bin/cpack.exe"}, {"name": "CMAKE_CTEST_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to ctest program executable."}], "type": "INTERNAL", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/bin/ctest.exe"}, {"name": "CMAKE_CXX_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "CXX compiler"}], "type": "FILEPATH", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/g++.exe"}, {"name": "CMAKE_CXX_COMPILER_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"}], "type": "FILEPATH", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/gcc-ar.exe"}, {"name": "CMAKE_CXX_COMPILER_RANLIB", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"}], "type": "FILEPATH", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/gcc-ranlib.exe"}, {"name": "CMAKE_CXX_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_CXX_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during DEBUG builds."}], "type": "STRING", "value": "-g"}, {"name": "CMAKE_CXX_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during MINSIZEREL builds."}], "type": "STRING", "value": "-Os -DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELEASE builds."}], "type": "STRING", "value": "-O3 -DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "-O2 -g -DNDEBUG"}, {"name": "CMAKE_CXX_STANDARD_LIBRARIES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Libraries linked by default with all C++ applications."}], "type": "STRING", "value": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32"}, {"name": "CMAKE_C_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C compiler"}], "type": "FILEPATH", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/gcc.exe"}, {"name": "CMAKE_C_COMPILER_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"}], "type": "FILEPATH", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/gcc-ar.exe"}, {"name": "CMAKE_C_COMPILER_RANLIB", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"}], "type": "FILEPATH", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/gcc-ranlib.exe"}, {"name": "CMAKE_C_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_C_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during DEBUG builds."}], "type": "STRING", "value": "-g"}, {"name": "CMAKE_C_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during MINSIZEREL builds."}], "type": "STRING", "value": "-Os -DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELEASE builds."}], "type": "STRING", "value": "-O3 -DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "-O2 -g -DNDEBUG"}, {"name": "CMAKE_C_STANDARD_LIBRARIES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Libraries linked by default with all C applications."}], "type": "STRING", "value": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32"}, {"name": "CMAKE_DLLTOOL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/dlltool.exe"}, {"name": "CMAKE_EXECUTABLE_FORMAT", "properties": [{"name": "HELPSTRING", "value": "Executable file format"}], "type": "INTERNAL", "value": "Unknown"}, {"name": "CMAKE_EXE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXPORT_COMPILE_COMMANDS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable/Disable output of compile commands during generation."}], "type": "BOOL", "value": ""}, {"name": "CMAKE_EXTRA_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of external makefile project generator."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_FIND_PACKAGE_REDIRECTS_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake."}], "type": "STATIC", "value": "D:/Desktop_xiao/chat-forge-master/server/cmake-build-debug/CMakeFiles/pkgRedirects"}, {"name": "CMAKE_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of generator."}], "type": "INTERNAL", "value": "Ninja"}, {"name": "CMAKE_GENERATOR_INSTANCE", "properties": [{"name": "HELPSTRING", "value": "Generator instance identifier."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_PLATFORM", "properties": [{"name": "HELPSTRING", "value": "Name of generator platform."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_TOOLSET", "properties": [{"name": "HELPSTRING", "value": "Name of generator toolset."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GNUtoMS", "properties": [{"name": "HELPSTRING", "value": "Convert GNU import libraries to MS format (requires Visual Studio)"}], "type": "BOOL", "value": "OFF"}, {"name": "CMAKE_HAVE_LIBC_PTHREAD", "properties": [{"name": "HELPSTRING", "value": "Test CMAKE_HAVE_LIBC_PTHREAD"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_HOME_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": "Source directory with the top level CMakeLists.txt file for this project"}], "type": "INTERNAL", "value": "D:/Desktop_xiao/chat-forge-master/server"}, {"name": "CMAKE_INSTALL_BINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "User executables (bin)"}], "type": "PATH", "value": "bin"}, {"name": "CMAKE_INSTALL_DATADIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data (DATAROOTDIR)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_DATAROOTDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data root (share)"}], "type": "PATH", "value": "share"}, {"name": "CMAKE_INSTALL_DOCDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Documentation root (DATAROOTDIR/doc/PROJECT_NAME)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_INCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files (include)"}], "type": "PATH", "value": "include"}, {"name": "CMAKE_INSTALL_INFODIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Info documentation (DATAROOTDIR/info)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LIBDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Object code libraries (lib)"}], "type": "PATH", "value": "lib"}, {"name": "CMAKE_INSTALL_LIBEXECDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Program executables (libexec)"}], "type": "PATH", "value": "libexec"}, {"name": "CMAKE_INSTALL_LOCALEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Locale-dependent data (DATAROOTDIR/locale)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LOCALSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable single-machine data (var)"}], "type": "PATH", "value": "var"}, {"name": "CMAKE_INSTALL_MANDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Man documentation (DATAROOTDIR/man)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_OLDINCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files for non-gcc (/usr/include)"}], "type": "PATH", "value": "/usr/include"}, {"name": "CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "Install path prefix, prepended onto install directories."}], "type": "PATH", "value": "C:/Program Files (x86)/server"}, {"name": "CMAKE_INSTALL_RUNSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Run-time variable data (LOCALSTATEDIR/run)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_SBINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "System admin executables (sbin)"}], "type": "PATH", "value": "sbin"}, {"name": "CMAKE_INSTALL_SHAREDSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable architecture-independent data (com)"}], "type": "PATH", "value": "com"}, {"name": "CMAKE_INSTALL_SYSCONFDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only single-machine data (etc)"}], "type": "PATH", "value": "etc"}, {"name": "CMAKE_LINKER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/ld.exe"}, {"name": "CMAKE_MAKE_PROGRAM", "properties": [{"name": "HELPSTRING", "value": "make program"}], "type": "FILEPATH", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/ninja/win/x64/ninja.exe"}, {"name": "CMAKE_MODULE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_NM", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/nm.exe"}, {"name": "CMAKE_NUMBER_OF_MAKEFILES", "properties": [{"name": "HELPSTRING", "value": "number of local generators"}], "type": "INTERNAL", "value": "4"}, {"name": "CMAKE_OBJCOPY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/objcopy.exe"}, {"name": "CMAKE_OBJDUMP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/objdump.exe"}, {"name": "CMAKE_PLATFORM_INFO_INITIALIZED", "properties": [{"name": "HELPSTRING", "value": "Platform information initialized"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_PROJECT_DESCRIPTION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_HOMEPAGE_URL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_NAME", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "server"}, {"name": "CMAKE_PROJECT_VERSION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "3.11.2"}, {"name": "CMAKE_PROJECT_VERSION_MAJOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "3"}, {"name": "CMAKE_PROJECT_VERSION_MINOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "11"}, {"name": "CMAKE_PROJECT_VERSION_PATCH", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "2"}, {"name": "CMAKE_PROJECT_VERSION_TWEAK", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_RANLIB", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/ranlib.exe"}, {"name": "CMAKE_RC_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "RC compiler"}], "type": "FILEPATH", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/windres.exe"}, {"name": "CMAKE_RC_COMPILER_WORKS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_RC_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_RC_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_RC_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_RC_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_RC_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_READELF", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/readelf.exe"}, {"name": "CMAKE_ROOT", "properties": [{"name": "HELPSTRING", "value": "Path to CMake installation."}], "type": "INTERNAL", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28"}, {"name": "CMAKE_SHARED_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SKIP_INSTALL_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when installing shared libraries, but are added when building."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_SKIP_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when using shared libraries."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_STATIC_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STRIP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/strip.exe"}, {"name": "CMAKE_TAPI", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_TAPI-NOTFOUND"}, {"name": "CMAKE_VERBOSE_MAKEFILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If this value is on, makefiles will be generated without the .SILENT directive, and all commands will be echoed to the console during the make.  This is useful for debugging only. With Visual Studio IDE projects all commands are done without /nologo."}], "type": "BOOL", "value": "FALSE"}, {"name": "CPPCHECK_EXECUTABLE", "properties": [{"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CPPCHECK_EXECUTABLE-NOTFOUND"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PythonInterp", "properties": [{"name": "HELPSTRING", "value": "Details about finding PythonInterp"}], "type": "INTERNAL", "value": "[E:/YingYong/py3.8/python.exe][v3.8.10()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Threads", "properties": [{"name": "HELPSTRING", "value": "Details about finding Threads"}], "type": "INTERNAL", "value": "[TRUE][v()]"}, {"name": "JSON_BuildTests", "properties": [{"name": "HELPSTRING", "value": "Build the unit tests when BUILD_TESTING is enabled."}], "type": "BOOL", "value": "OFF"}, {"name": "JSON_CI", "properties": [{"name": "HELPSTRING", "value": "Enable CI build targets."}], "type": "BOOL", "value": "OFF"}, {"name": "JSON_Diagnostics", "properties": [{"name": "HELPSTRING", "value": "Use extended diagnostic messages."}], "type": "BOOL", "value": "OFF"}, {"name": "JSON_DisableEnumSerialization", "properties": [{"name": "HELPSTRING", "value": "Disable default integer enum serialization."}], "type": "BOOL", "value": "OFF"}, {"name": "JSON_GlobalUDLs", "properties": [{"name": "HELPSTRING", "value": "Place use-defined string literals in the global namespace."}], "type": "BOOL", "value": "ON"}, {"name": "JSON_ImplicitConversions", "properties": [{"name": "HELPSTRING", "value": "Enable implicit conversions."}], "type": "BOOL", "value": "ON"}, {"name": "JSON_Install", "properties": [{"name": "HELPSTRING", "value": "Install CMake targets during install step."}], "type": "BOOL", "value": "OFF"}, {"name": "JSON_LegacyDiscardedValueComparison", "properties": [{"name": "HELPSTRING", "value": "Enable legacy discarded value comparison."}], "type": "BOOL", "value": "OFF"}, {"name": "JSON_MultipleHeaders", "properties": [{"name": "HELPSTRING", "value": "Use non-amalgamated version of the library."}], "type": "BOOL", "value": "ON"}, {"name": "JSON_SystemInclude", "properties": [{"name": "HELPSTRING", "value": "Include as system headers (skip for clang-tidy)."}], "type": "BOOL", "value": "OFF"}, {"name": "NLOHMANN_JSON_CONFIG_INSTALL_DIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "share/cmake/nlo<PERSON>_json"}, {"name": "PYTHON_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "E:/YingYong/py3.8/python.exe"}, {"name": "SQLITECPP_BUILD_EXAMPLES", "properties": [{"name": "HELPSTRING", "value": "Build examples."}], "type": "BOOL", "value": "OFF"}, {"name": "SQLITECPP_BUILD_TESTS", "properties": [{"name": "HELPSTRING", "value": "Build and run tests."}], "type": "BOOL", "value": "OFF"}, {"name": "SQLITECPP_DISABLE_STD_FILESYSTEM", "properties": [{"name": "HELPSTRING", "value": "Disable the use of std::filesystem in SQLiteCpp."}], "type": "BOOL", "value": "OFF"}, {"name": "SQLITECPP_INCLUDE_SCRIPT", "properties": [{"name": "HELPSTRING", "value": "Include config & script files."}], "type": "BOOL", "value": "ON"}, {"name": "SQLITECPP_INTERNAL_SQLITE", "properties": [{"name": "HELPSTRING", "value": "Add the internal SQLite3 source to the project."}], "type": "BOOL", "value": "ON"}, {"name": "SQLITECPP_RUN_CPPCHECK", "properties": [{"name": "HELPSTRING", "value": "Run cppcheck C++ static analysis tool."}], "type": "BOOL", "value": "ON"}, {"name": "SQLITECPP_RUN_CPPLINT", "properties": [{"name": "HELPSTRING", "value": "Run cpplint.py tool for Google C++ StyleGuide."}], "type": "BOOL", "value": "ON"}, {"name": "SQLITECPP_RUN_DOXYGEN", "properties": [{"name": "HELPSTRING", "value": "Run Doxygen C++ documentation tool."}], "type": "BOOL", "value": "OFF"}, {"name": "SQLITECPP_USE_ASAN", "properties": [{"name": "HELPSTRING", "value": "Use Address Sanitizer."}], "type": "BOOL", "value": "OFF"}, {"name": "SQLITECPP_USE_GCOV", "properties": [{"name": "HELPSTRING", "value": "USE GCov instrumentation."}], "type": "BOOL", "value": "OFF"}, {"name": "SQLITE_ENABLE_ASSERT_HANDLER", "properties": [{"name": "HELPSTRING", "value": "Enable the user definition of a assertion_failed() handler."}], "type": "BOOL", "value": "OFF"}, {"name": "SQLITE_ENABLE_COLUMN_METADATA", "properties": [{"name": "HELPSTRING", "value": "Enable Column::getColumnOriginName(). Require support from sqlite3 library."}], "type": "BOOL", "value": "ON"}, {"name": "SQLITE_ENABLE_JSON1", "properties": [{"name": "HELPSTRING", "value": "Enable JSON1 extension when building internal sqlite3 library."}], "type": "BOOL", "value": "ON"}, {"name": "SQLITE_HAS_CODEC", "properties": [{"name": "HELPSTRING", "value": "Enable database encryption API. Not available in the public release of SQLite."}], "type": "BOOL", "value": "OFF"}, {"name": "SQLITE_OMIT_LOAD_EXTENSION", "properties": [{"name": "HELPSTRING", "value": "Enable omit load extension"}], "type": "BOOL", "value": "OFF"}, {"name": "SQLITE_USE_LEGACY_STRUCT", "properties": [{"name": "HELPSTRING", "value": "Fallback to forward declaration of legacy struct sqlite3_value (pre SQLite 3.19)"}], "type": "BOOL", "value": "OFF"}, {"name": "SQLiteCpp_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/Desktop_xiao/chat-forge-master/server/cmake-build-debug/thirdparty/SQLiteCpp"}, {"name": "SQLiteCpp_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "SQLiteCpp_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;SQLite::SQLite3;"}, {"name": "SQLiteCpp_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/Desktop_xiao/chat-forge-master/server/thirdparty/sQLitecpp"}, {"name": "_CMAKE_LINKER_PUSHPOP_STATE_SUPPORTED", "properties": [{"name": "HELPSTRING", "value": "linker supports push/pop state"}], "type": "INTERNAL", "value": "TRUE"}, {"name": "_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "CMAKE_INSTALL_PREFIX during last run"}], "type": "INTERNAL", "value": "C:/Program Files (x86)/server"}, {"name": "n<PERSON><PERSON>_<PERSON>son_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/Desktop_xiao/chat-forge-master/server/cmake-build-debug/thirdparty/json-develop"}, {"name": "n<PERSON><PERSON>_json_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "n<PERSON><PERSON>_<PERSON><PERSON>_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/Desktop_xiao/chat-forge-master/server/thirdparty/json-develop"}, {"name": "server_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/Desktop_xiao/chat-forge-master/server/cmake-build-debug"}, {"name": "server_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "ON"}, {"name": "server_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/Desktop_xiao/chat-forge-master/server"}], "kind": "cache", "version": {"major": 2, "minor": 0}}